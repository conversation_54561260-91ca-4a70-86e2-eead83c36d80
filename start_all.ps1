# Definir o diretório do script para uso consistente
$PSScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Definition
Set-Location $PSScriptRoot # Garante que os comandos docker-compose sejam executados no diretório correto

# --- Configurações Personalizáveis ---
$DomainForN8n = "n8n.automisa.com" # Este é o domínio que será usado no Traefik e adicionado ao hosts
$N8N_URL_VIA_TRAEFIK = "https://$DomainForN8n"
$TRAEFIK_DASHBOARD_URL = "http://localhost:8081"
$EVOLUTION_API_URL = "http://localhost:8080"
$EVOLUTION_MANAGER_URL = "$EVOLUTION_API_URL/manager"
# ------------------------------------

Function Write-SectionHeader {
    param([string]$Title)
    Write-Host "=================================================================" -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Cyan
    Write-Host "=================================================================" -ForegroundColor Cyan
}

Function Test-IsAdmin {
    $identity = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($identity)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

$IsAdmin = Test-IsAdmin

Write-SectionHeader "Script de Inicializacao do Ambiente Docker n8n + Evolution API"
Write-Host " Localizacao do Script: $PSScriptRoot"
if ($IsAdmin) {
    Write-Host "[INFO] Script esta rodando com privilegios de Administrador." -ForegroundColor Green
} else {
    Write-Host "[AVISO] Script NAO esta rodando com privilegios de Administrador." -ForegroundColor Yellow
    Write-Host "         A modificacao automatica do arquivo hosts para '$DomainForN8n' nao sera possivel." -ForegroundColor Yellow
}
Write-Host ""

# 1. Verifica se o Docker está rodando
Write-SectionHeader "Verificando Status do Docker"
try {
    docker ps > $null 2>&1
    Write-Host "[INFO] Docker esta rodando." -ForegroundColor Green
} catch {
    Write-Host "[ERRO] Docker nao parece estar rodando." -ForegroundColor Red
    Write-Host "       Por favor, inicie o Docker Desktop e tente novamente."
    Read-Host "Pressione Enter para sair"
    exit 1
}
Write-Host ""

# 2. Verifica o arquivo .env e N8N_ENCRYPTION_KEY
Write-SectionHeader "Verificando Arquivo .env e N8N_ENCRYPTION_KEY"
$EnvFilePath = Join-Path -Path $PSScriptRoot -ChildPath ".env"
if (-not (Test-Path $EnvFilePath)) {
    Write-Host "[AVISO] Arquivo .env NAO ENCONTRADO em '$EnvFilePath'." -ForegroundColor Yellow
    Write-Host "        Este arquivo e necessario e deve conter N8N_ENCRYPTION_KEY e TZ (entre outras variaveis para a Evolution API)."
    Write-Host "        O script tentara continuar, mas o n8n pode nao iniciar corretamente sem N8N_ENCRYPTION_KEY."
} else {
    Write-Host "[INFO] Arquivo .env encontrado." -ForegroundColor Green
    $EnvContent = Get-Content $EnvFilePath -Raw
    if ($EnvContent -notmatch "^\s*N8N_ENCRYPTION_KEY\s*=\s*.+") {
        Write-Host "[AVISO] A variavel N8N_ENCRYPTION_KEY nao foi encontrada ou nao esta definida no arquivo .env." -ForegroundColor Yellow
        Write-Host "        Esta chave e crucial para a criptografia dos dados do n8n."
        Write-Host "        Certifique-se de que ela esteja definida como uma string aleatoria e segura (ex: N8N_ENCRYPTION_KEY=minhachavesupersecreta)."
    } else {
        Write-Host "[INFO] N8N_ENCRYPTION_KEY parece estar presente no .env (valor nao verificado)." -ForegroundColor Green
    }
}
Write-Host ""

# 3. Lembrete sobre Versionamento de Imagens
Write-SectionHeader "Lembrete Importante: Versionamento de Imagens Docker"
Write-Host "[AVISO] Para ambientes de producao e maior estabilidade, e ALTAMENTE RECOMENDADO" -ForegroundColor Yellow
Write-Host "        fixar as versoes das imagens Docker no arquivo 'docker-compose.yaml' em vez de usar ':latest'." -ForegroundColor Yellow
Write-Host "        Ex: 'image: n8nio/n8n:1.43.2' em vez de 'image: n8nio/n8n:latest'." -ForegroundColor Yellow
Write-Host ""

# 4. Modificacao do Arquivo Hosts (se admin)
if ($IsAdmin) {
    Write-SectionHeader "Configuracao do Arquivo Hosts (para $DomainForN8n)"
    $HostsFilePath = Join-Path -Path $env:windir -ChildPath "System32\drivers\etc\hosts"
    $IpAddress = "127.0.0.1"
    $HostEntryContent = "$IpAddress`t$DomainForN8n"
    $HostEntryComment = "# Adicionado por start_all.ps1 para $DomainForN8n em $N8N_URL_VIA_TRAEFIK"
    $FullHostEntry = "$HostEntryContent`t$HostEntryComment"

    $HostsFileContent = Get-Content $HostsFilePath -ErrorAction SilentlyContinue
    $EntryExists = $false
    if ($HostsFileContent) {
        foreach ($line in $HostsFileContent) {
            if ($line -match "^\s*$([regex]::Escape($IpAddress))\s+$([regex]::Escape($DomainForN8n))(?:\s*#.*)?$") {
                if ($line.TrimStart() -notlike "#*") {
                    $EntryExists = $true
                    break
                }
            }
        }
    }

    if (-not $EntryExists) {
        Write-Host "[INFO] Tentando adicionar '$DomainForN8n -> $IpAddress' ao arquivo hosts ($HostsFilePath)..." -ForegroundColor Yellow
        try {
            Add-Content -Path $HostsFilePath -Value "`n$FullHostEntry" -ErrorAction Stop
            Write-Host "[SUCESSO] Entrada '$DomainForN8n' adicionada ao arquivo hosts." -ForegroundColor Green
            Write-Host "           Pode ser necessario limpar o cache DNS do sistema. Voce pode tentar executar:" -ForegroundColor Green
            Write-Host "           `ipconfig /flushdns` (em um CMD ou PowerShell como Administrador)." -ForegroundColor Green
        } catch {
            Write-Host "[ERRO] Falha ao adicionar entrada ao arquivo hosts. $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "        Verifique se o script esta realmente rodando como Administrador e se nao ha problemas de permissao no arquivo." -ForegroundColor Red
        }
    } else {
        Write-Host "[INFO] Entrada para '$DomainForN8n' ($IpAddress) ja existe no arquivo hosts." -ForegroundColor Green
    }
    Write-Host ""
} else {
    Write-SectionHeader "Configuracao do Arquivo Hosts (Ignorada)"
    Write-Host "[AVISO] O script nao esta rodando como Administrador." -ForegroundColor Yellow
    Write-Host "         A entrada para '$DomainForN8n' (apontando para 127.0.0.1) NAO sera adicionada ao arquivo hosts." -ForegroundColor Yellow
    Write-Host "         Para acessar o n8n via '$N8N_URL_VIA_TRAEFIK' no seu navegador localmente," -ForegroundColor Yellow
    Write-Host "         voce precisara adicionar '127.0.0.1 $DomainForN8n' ao seu arquivo hosts manualmente" -ForegroundColor Yellow
    Write-Host "         (C:\Windows\System32\drivers\etc\hosts) ou executar este script como Administrador." -ForegroundColor Yellow
    Write-Host ""
}


# 5. Nomes dos containers principais e verificação de ambiente existente
$TRAEFIK_CONTAINER_NAME = "traefik"
$EVOLUTION_CONTAINER_NAME = "evolution_api"
$N8N_CONTAINER_NAME = "n8n"
$AMBIENTE_EXISTE = 0

$traefik_running = docker ps -a --filter "name=$TRAEFIK_CONTAINER_NAME" --format "{{.Names}}" | Select-String -Pattern "^$TRAEFIK_CONTAINER_NAME$" -Quiet
$evolution_running = docker ps -a --filter "name=$EVOLUTION_CONTAINER_NAME" --format "{{.Names}}" | Select-String -Pattern "^$EVOLUTION_CONTAINER_NAME$" -Quiet
$n8n_running = docker ps -a --filter "name=$N8N_CONTAINER_NAME" --format "{{.Names}}" | Select-String -Pattern "^$N8N_CONTAINER_NAME$" -Quiet

if ($traefik_running -and $evolution_running -and $n8n_running) {
    $AMBIENTE_EXISTE = 1
}

Function Run-DockerComposeCommand {
    param(
        [string]$Command,
        [string]$ErrorMessage
    )
    Write-Host "Executando: docker compose $Command" # Mudança aqui
    docker compose $Command                          # Mudança aqui
    if ($LASTEXITCODE -ne 0) {
        Write-Host $ErrorMessage -ForegroundColor Red
        Write-Host "[INFO] Tentando validar a configuracao do docker-compose.yaml com 'docker compose config'..." -ForegroundColor Yellow # Mudança aqui
        docker compose config                          # Mudança aqui
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[ERRO] 'docker-compose config' tambem falhou. O arquivo docker-compose.yaml contem erros de sintaxe." -ForegroundColor Red
        } else {
            Write-Host "[INFO] 'docker-compose config' NAO reportou erros. O problema pode ser outro (ex: recursos, rede, imagens)." -ForegroundColor Yellow
        }
        Read-Host "Pressione Enter para sair"
        exit 1
    }
    return $true
}


if ($AMBIENTE_EXISTE -eq 1) {
    Write-SectionHeader "Ambiente Docker Existente Detectado"
    Write-Host "[INFO] Iniciando e atualizando containers existentes (se houver mudancas no docker-compose.yaml)..." -ForegroundColor Green
    Run-DockerComposeCommand -Command "up -d" -ErrorMessage "[ERRO] Falha ao iniciar/atualizar os containers existentes."
    Write-Host "[INFO] Containers iniciados/atualizados com sucesso." -ForegroundColor Green
} else {
    Write-SectionHeader "Nova Instalacao ou Recriacao do Ambiente"
    Write-Host "[INFO] Ambiente Docker nao encontrado ou incompleto." -ForegroundColor Yellow
    Write-Host "       Iniciando nova instalacao ou recriacao completa..."
    Write-Host ""

    $N8nDataBackupPath = Join-Path -Path $PSScriptRoot -ChildPath "n8n_data_backups"
    $N8nDataCurrentPath = Join-Path -Path $PSScriptRoot -ChildPath "n8n_data"
    if (Test-Path $N8nDataCurrentPath) {
        Write-SectionHeader "Backup da Pasta n8n_data"
        New-Item -ItemType Directory -Path $N8nDataBackupPath -Force -ErrorAction SilentlyContinue > $null
        $Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $BackupZipFileName = Join-Path -Path $N8nDataBackupPath -ChildPath "n8n_data_backup_$Timestamp.zip"
        Write-Host "  Tentando fazer backup de '$N8nDataCurrentPath' para '$BackupZipFileName'..."
        try {
            Compress-Archive -Path "$N8nDataCurrentPath\*" -DestinationPath $BackupZipFileName -Force -ErrorAction Stop
            Write-Host "  [SUCESSO] Backup concluido." -ForegroundColor Green
        } catch {
            Write-Host "  [AVISO] Falha ao criar backup da pasta n8n_data." -ForegroundColor Yellow
            Write-Host "          Erro: $($_.Exception.Message)"
        }
        Write-Host ""
    }

    Write-Host "Parando e removendo configuracoes antigas (se houver)..."
    docker-compose down -v --remove-orphans # Não encapsulado na função, pois um erro aqui pode ser normal se nada existir
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[AVISO] 'docker-compose down -v' retornou um codigo de saida nao zero. Pode nao haver nada para remover ou ocorreu um erro leve." -ForegroundColor Yellow
    }
    Write-Host ""

    Write-Host "Garantindo que as pastas para os volumes nomeados existem:"
    $VolumeDirectories = @( "evolution_instances", "evolution_store", "evolution_redis_data", "evolution_postgres_data", "n8n_data", "traefik_letsencrypt" )
    foreach ($dir in $VolumeDirectories) {
        New-Item -ItemType Directory -Path (Join-Path -Path $PSScriptRoot -ChildPath $dir) -Force -ErrorAction SilentlyContinue > $null
    }
    Write-Host "  - Pastas de volumes verificadas/criadas." -ForegroundColor Green
    Write-Host ""

    Write-SectionHeader "Verificacao do Diretorio de Dados do n8n"
    $N8nDataPath = Join-Path -Path $PSScriptRoot -ChildPath "n8n_data"
    $N8nDbFileInHost = Join-Path -Path $N8nDataPath -ChildPath "database.sqlite"
    Write-Host "Verificando o diretorio de dados do n8n: '$N8nDataPath'"
    Write-Host "  (O docker-compose.yaml esta configurado para mapear este diretorio para '/home/<USER>/.n8n/' no container n8n)"
    if (Test-Path $N8nDbFileInHost) {
        Write-Host "  [INFO] Arquivo database.sqlite ENCONTRADO em: $N8nDbFileInHost" -ForegroundColor Green
        try { $fileInfo = Get-Item $N8nDbFileInHost; Write-Host "         Tamanho: $($fileInfo.Length) bytes."; Write-Host "         Ultima modificacao: $($fileInfo.LastWriteTime)." } catch {}
        Write-Host "         O n8n utilizara este banco de dados existente."
    } else {
        Write-Host "  [INFO] Arquivo database.sqlite NAO ENCONTRADO em '$N8nDbFileInHost' (dentro de '$N8nDataPath')." -ForegroundColor Green 
        Write-Host "          O n8n criara um novo arquivo database.sqlite dentro do diretorio '$N8nDataPath' quando iniciar."
    }
    Write-Host ""

    Write-SectionHeader "Construindo e Iniciando os Containers"
    Write-Host "(Pode levar alguns minutos na primeira vez ou se houver atualizacoes de imagens...)"
    Run-DockerComposeCommand -Command "up --build -d" -ErrorMessage "[ERRO] Falha ao construir ou iniciar os containers."
    Write-Host ""
    Write-Host "[SUCESSO] Nova instalacao/recriacao concluida!" -ForegroundColor Green
}
Write-Host ""

Write-SectionHeader "Aguardando Estabilizacao dos Containers"
Write-Host "Os containers estao iniciando e estabilizando..."
Start-Sleep -Seconds 30 # Aumentado um pouco mais para dar tempo a todos os serviços

Write-Host ""
Write-SectionHeader "Logs Recentes dos Containers"
$LogServices = @(
    @{Name="Traefik"; ContainerName=$TRAEFIK_CONTAINER_NAME}, @{Name="Evolution API"; ContainerName=$EVOLUTION_CONTAINER_NAME},
    @{Name="PostgreSQL"; ContainerName="postgres"}, @{Name="Redis"; ContainerName="redis"},
    @{Name="n8n"; ContainerName=$N8N_CONTAINER_NAME}, @{Name="Memcached"; ContainerName="memcached"}
)
foreach ($service in $LogServices) {
    Write-Host "--- Logs $($service.Name) (ultimas 20 linhas) ---" -ForegroundColor Yellow
    docker-compose logs --tail="20" $service.ContainerName
}
Write-Host ""

Write-SectionHeader "Verificacao de Conteudo dos Diretorios de Dados"
$DataDirectoriesToVerify = @( "n8n_data", "evolution_store", "evolution_redis_data", "evolution_postgres_data", "evolution_instances", "traefik_letsencrypt" )
foreach ($dir in $DataDirectoriesToVerify) {
    $FullPath = Join-Path -Path $PSScriptRoot -ChildPath $dir
    Write-Host "Conteudo de: $FullPath" -ForegroundColor Yellow
    if (Test-Path $FullPath) { Get-ChildItem -Path $FullPath | Format-Table Mode, LastWriteTime, Length, Name -AutoSize } else { Write-Host "  Diretorio nao encontrado." }
    Write-Host ""
}

Write-Host ""
Write-SectionHeader "Ambiente Iniciado!"
Write-Host ""
Write-Host "URLs para acesso:" -ForegroundColor Green
Write-Host "  Evolution API:     $EVOLUTION_API_URL"
Write-Host "  Evolution Manager: $EVOLUTION_MANAGER_URL"
Write-Host "  n8n (via Traefik): $N8N_URL_VIA_TRAEFIK"
Write-Host "  Traefik Dashboard: $TRAEFIK_DASHBOARD_URL"
Write-Host ""

$CanOpenN8nDirectly = $false
if ($DomainForN8n -eq "localhost" -or $DomainForN8n -eq "127.0.0.1") { $CanOpenN8nDirectly = $true }
elseif ($IsAdmin) { 
    $CanOpenN8nDirectly = $true
    Write-Host "[INFO] Como o script rodou como Admin, a entrada para '$DomainForN8n' deve estar no arquivo hosts (se nao existia)." -ForegroundColor Green
    Write-Host "       Tentando abrir '$N8N_URL_VIA_TRAEFIK' no navegador." -ForegroundColor Green
}

if ($CanOpenN8nDirectly) {
    Write-Host "Abrindo n8n e Evolution Manager no navegador em 10 segundos..."
    Start-Sleep -Seconds 10
    Start-Process $N8N_URL_VIA_TRAEFIK
    Start-Process $EVOLUTION_MANAGER_URL
} else {
    Write-Host "Para acessar o n8n no navegador, use: $N8N_URL_VIA_TRAEFIK" -ForegroundColor Yellow
    Write-Host "Se voce NAO executou este script como Administrador, a entrada '$DomainForN8n -> 127.0.0.1'" -ForegroundColor Yellow
    Write-Host "NAO foi adicionada automaticamente ao seu arquivo hosts. Voce precisara faze-lo manualmente" -ForegroundColor Yellow
    Write-Host "ou executar o script como Administrador para que o navegador resolva '$DomainForN8n' localmente." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Pressione Enter para fechar este script..."
Read-Host
exit 0