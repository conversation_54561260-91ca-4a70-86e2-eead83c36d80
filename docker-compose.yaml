# version: '3.8'

services:
  traefik:
    image: traefik:v2.11 # Use uma versão específica
    container_name: traefik
    command:
      - "--api.insecure=true" # Para dashboard (opcional, pode ser protegido)
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.myresolver.acme.tlschallenge=true"
      # - "--certificatesresolvers.myresolver.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory" # Para testes
      - "--certificatesresolvers.myresolver.acme.email=<EMAIL>"
      - "--certificatesresolvers.myresolver.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
      - "8081:8080" # Para o dashboard do Traefik, se --api.insecure=true
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./traefik_letsencrypt:/letsencrypt"
    networks:
      - app-network # Ou uma rede específica para o proxy se preferir
    restart: always

  evolution_api:
    container_name: evolution_api
    image: atendai/evolution-api:latest
    restart: always
    depends_on:
      - redis
      - postgres
    ports:
      - "8080:8080"
    volumes:
      - ./evolution_instances:/evolution/instances
      - ./evolution_store:/evolution/store
    networks:
      - app-network
    env_file:
      - .env

  redis:
    container_name: redis
    image: redis:latest
    networks:
      - app-network
    command: >
      redis-server --port 6379 --appendonly yes
    volumes:
      - ./evolution_redis_data:/data
    ports:
      - "6379:6379"

  postgres:
    container_name: postgres
    image: postgres:15
    networks:
      - app-network
    command: ["postgres", "-c", "max_connections=1000", "-c", "listen_addresses=*"]
    restart: always
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=misa          # Correto
      - POSTGRES_PASSWORD=senha     # Correto
      - POSTGRES_DB=evolution       # Correto
      - POSTGRES_HOST_AUTH_METHOD=trust # Correto
    # REMOVA AS LINHAS ABAIXO DESTE BLOCO:
    # - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
    # - DB_TYPE=postgresdb
    # - DB_POSTGRESDB_HOST=postgres
    # - DB_POSTGRESDB_PORT=5432
    # - DB_POSTGRESDB_DATABASE=n8n_db
    # - DB_POSTGRESDB_USER=n8n_user
    # - DB_POSTGRESDB_PASSWORD=${N8N_DB_PASSWORD}
    volumes:
      - ./evolution_postgres_data:/var/lib/postgresql/data

  n8n:
    container_name: n8n
    image: n8nio/n8n:latest
    restart: always
    #ports:
    #  - "5678:5678"
    networks:
      - app-network
    environment:
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - GENERIC_TIMEZONE=${TZ}
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
      - N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true
      - N8N_EDITOR_BASE_URL=https://n8n.automisa.com  # <--- Esta linha é a chave
      - N8N_WEBHOOK_URL=https://n8n.automisa.com
      - N8N_LOG_LEVEL=info
      # Configurações para workers e fila com Redis (usando o serviço 'redis' existente)
      - N8N_RUNNERS_ENABLED=true
      - N8N_QUEUE_MODE=bullmq_redis
      - N8N_QUEUE_BULL_REDIS_HOST=redis
      - N8N_QUEUE_BULL_REDIS_PORT=6379
      # - N8N_QUEUE_BULL_REDIS_PASSWORD=${REDIS_PASSWORD_N8N} # Se seu Redis tiver senha e você quiser usar uma específica para o n8n
      # - N8N_QUEUE_BULL_REDIS_DB=1 # Para usar um DB Redis diferente para a fila do n8n      
      - N8N_CACHE_TYPE=memcached
      - N8N_CACHE_MEMCACHED_HOSTS=memcached:11211
      - N8N_USER_FOLDER=/home/<USER>/.n8n/
      # Configurações SQLite
      - DB_SQLITE_VACUUM_ON_STARTUP=true  
      # ... (outras variáveis do n8n) ...
      # - DB_TYPE=postgresdb  # Adicionar
      # - DB_POSTGRESDB_HOST=postgres # Adicionar
      # - DB_POSTGRESDB_PORT=5432 # Adicionar
      # - DB_POSTGRESDB_DATABASE=n8n_db # Adicionar (e crie este DB no Postgres)
      # - DB_POSTGRESDB_USER=n8n_user   # Adicionar (e crie este usuário no Postgres)
      # - DB_POSTGRESDB_PASSWORD=${N8N_DB_PASSWORD} # Adicionar (defina no .env)


      
    volumes:
      - ./n8n_data:/home/<USER>/.n8n/                   # <--- montar o diretório inteiro para persistir todos os dados do n8n (incluindo arquivos de configuração, dados binários, etc.)
      # - ./n8n_data/database.sqlite:/home/<USER>/.n8n/database.sqlite # <<< ALTERAÇÃO AQUI
      # Se precisar de subpastas como binaryData, adicione-as separadamente se necessário:
      # - ./n8n_data/binaryData:/home/<USER>/.n8n/binaryData
    depends_on: # Garante que os serviços de backend estejam disponíveis antes do n8n iniciar
      - redis
      - memcached
      # - postgres # Se o n8n fosse usar postgres, mas está usando SQLite
    labels: # Labels para Traefik descobrir e configurar o n8n
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`n8n.automisa.com`)"
      - "traefik.http.routers.n8n.entrypoints=websecure"
      - "traefik.http.routers.n8n.tls.certresolver=myresolver"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"
      # Opcional: Redirecionar HTTP para HTTPS
      - "traefik.http.routers.n8n-http.rule=Host(`n8n.automisa.com`)"
      - "traefik.http.routers.n8n-http.entrypoints=web"
      - "traefik.http.routers.n8n-http.middlewares=redirect-to-https@docker"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.permanent=true"
    
  memcached:
    container_name: memcached
    image: memcached:latest
    ports:
      - "11211:11211"
    networks:
      - app-network
    restart: always

# volumes:  # <--- Comente ou remova esta seção se n8n_db for o único volume aqui
#   n8n_db:
#     driver: local
#     driver_opts:
#       type: none
#       o: bind
#       device: ./n8n_data/database.sqlite # Este estava causando o erro "not a directory"e
  # Removidos volumes não utilizados: evolution_instances, evolution_redis, postgres_data

networks:
  app-network:
    name: app-network
    driver: bridge